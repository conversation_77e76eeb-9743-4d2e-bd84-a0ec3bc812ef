﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/algo-course.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\algo-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-zr5YMVDF2QOwhOua3aMnhmblFaVnsYj01b\u002BK1MFuQLg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"133912"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022zr5YMVDF2QOwhOua3aMnhmblFaVnsYj01b\u002BK1MFuQLg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/algo-course.y8fwg3ri7a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\algo-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"y8fwg3ri7a"},{"Name":"integrity","Value":"sha256-zr5YMVDF2QOwhOua3aMnhmblFaVnsYj01b\u002BK1MFuQLg="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/algo-course.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"133912"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022zr5YMVDF2QOwhOua3aMnhmblFaVnsYj01b\u002BK1MFuQLg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/algo.my4xpyu51w.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\algo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"my4xpyu51w"},{"Name":"integrity","Value":"sha256-oscRLo9rnzz0qoPoJbwl9i8xT9vD2lBDOhlG6btklSg="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/algo.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1033"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022oscRLo9rnzz0qoPoJbwl9i8xT9vD2lBDOhlG6btklSg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/algo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\algo.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-oscRLo9rnzz0qoPoJbwl9i8xT9vD2lBDOhlG6btklSg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"1033"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022oscRLo9rnzz0qoPoJbwl9i8xT9vD2lBDOhlG6btklSg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/book.a7xxyo5s14.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\book.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"a7xxyo5s14"},{"Name":"integrity","Value":"sha256-qsdV41cq18PxjIolYCPAlmPu2USoa2/3ZH85DXYhnlE="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/book.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"702"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qsdV41cq18PxjIolYCPAlmPu2USoa2/3ZH85DXYhnlE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/book.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\book.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-qsdV41cq18PxjIolYCPAlmPu2USoa2/3ZH85DXYhnlE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"702"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022qsdV41cq18PxjIolYCPAlmPu2USoa2/3ZH85DXYhnlE=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/c-course.8iqelb7f6u.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\c-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"8iqelb7f6u"},{"Name":"integrity","Value":"sha256-sO6Wj2X/2mRV9eDii4JKyIYR807wMNBCCJuZ4XhpuD4="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/c-course.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"50278"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022sO6Wj2X/2mRV9eDii4JKyIYR807wMNBCCJuZ4XhpuD4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/c-course.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\c-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-sO6Wj2X/2mRV9eDii4JKyIYR807wMNBCCJuZ4XhpuD4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"50278"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022sO6Wj2X/2mRV9eDii4JKyIYR807wMNBCCJuZ4XhpuD4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/cn.9xn0eboqpd.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\cn.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9xn0eboqpd"},{"Name":"integrity","Value":"sha256-HVOlZzgj8c8sCXhNdS7YXTfElXZAGrKU4AvshX4f9b8="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/cn.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"13578"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022HVOlZzgj8c8sCXhNdS7YXTfElXZAGrKU4AvshX4f9b8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/cn.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\cn.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-HVOlZzgj8c8sCXhNdS7YXTfElXZAGrKU4AvshX4f9b8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"13578"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022HVOlZzgj8c8sCXhNdS7YXTfElXZAGrKU4AvshX4f9b8=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/coaa.epfyuncmgb.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\coaa.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"epfyuncmgb"},{"Name":"integrity","Value":"sha256-/qHn\u002BOhEdZf6xSVWa\u002BrUvNKi7BTRmDnM/XUkzLagFrM="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/coaa.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"393432"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022/qHn\u002BOhEdZf6xSVWa\u002BrUvNKi7BTRmDnM/XUkzLagFrM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/coaa.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\coaa.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/qHn\u002BOhEdZf6xSVWa\u002BrUvNKi7BTRmDnM/XUkzLagFrM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"393432"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022/qHn\u002BOhEdZf6xSVWa\u002BrUvNKi7BTRmDnM/XUkzLagFrM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/compiler.48ep7ugqte.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\compiler.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"48ep7ugqte"},{"Name":"integrity","Value":"sha256-RDPtvdtKIfp9LOr0ZL/v8xY0cKV3AGdPRbsCrOO3CAg="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/compiler.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"65133"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022RDPtvdtKIfp9LOr0ZL/v8xY0cKV3AGdPRbsCrOO3CAg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/compiler.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\compiler.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-RDPtvdtKIfp9LOr0ZL/v8xY0cKV3AGdPRbsCrOO3CAg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"65133"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022RDPtvdtKIfp9LOr0ZL/v8xY0cKV3AGdPRbsCrOO3CAg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/computer.htc0kdeeg9.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\computer.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"htc0kdeeg9"},{"Name":"integrity","Value":"sha256-G3cmbvcMtceMlncXvjk4hHcjK1xM2tIUzgICYr\u002BRKpM="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/computer.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"314"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022G3cmbvcMtceMlncXvjk4hHcjK1xM2tIUzgICYr\u002BRKpM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/computer.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\computer.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-G3cmbvcMtceMlncXvjk4hHcjK1xM2tIUzgICYr\u002BRKpM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"314"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022G3cmbvcMtceMlncXvjk4hHcjK1xM2tIUzgICYr\u002BRKpM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/d1.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\d1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-NNp5clbrLBgmS5zLqBYoNXEWuP\u002BVPd/z9mP3EyON4O0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3540"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NNp5clbrLBgmS5zLqBYoNXEWuP\u002BVPd/z9mP3EyON4O0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/d1.wdsa8pmrr9.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\d1.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"wdsa8pmrr9"},{"Name":"integrity","Value":"sha256-NNp5clbrLBgmS5zLqBYoNXEWuP\u002BVPd/z9mP3EyON4O0="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/d1.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3540"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022NNp5clbrLBgmS5zLqBYoNXEWuP\u002BVPd/z9mP3EyON4O0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/data-algo.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\data-algo.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-vx/TN2nBdyFTL33U/qL1Ql8BSi12oMmuTOhbVjp4q2A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"286114"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022vx/TN2nBdyFTL33U/qL1Ql8BSi12oMmuTOhbVjp4q2A=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/data-algo.vgm4lose1y.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\data-algo.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vgm4lose1y"},{"Name":"integrity","Value":"sha256-vx/TN2nBdyFTL33U/qL1Ql8BSi12oMmuTOhbVjp4q2A="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/data-algo.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"286114"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022vx/TN2nBdyFTL33U/qL1Ql8BSi12oMmuTOhbVjp4q2A=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/data-course.fnqno5tfx2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\data-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"fnqno5tfx2"},{"Name":"integrity","Value":"sha256-VT0fWL5F3JuSReOTcxRH\u002BrQwxbb8g4ytsbgfWuRkuqY="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/data-course.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"15046"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022VT0fWL5F3JuSReOTcxRH\u002BrQwxbb8g4ytsbgfWuRkuqY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/data-course.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\data-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-VT0fWL5F3JuSReOTcxRH\u002BrQwxbb8g4ytsbgfWuRkuqY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"15046"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022VT0fWL5F3JuSReOTcxRH\u002BrQwxbb8g4ytsbgfWuRkuqY=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/data.i9nyaihpyg.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\data.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"i9nyaihpyg"},{"Name":"integrity","Value":"sha256-Rsxu1SJZvug3H8V\u002BGLjQQSxN4BvqRrTv9Rc07cb7uGw="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/data.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"281"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Rsxu1SJZvug3H8V\u002BGLjQQSxN4BvqRrTv9Rc07cb7uGw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/data.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\data.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Rsxu1SJZvug3H8V\u002BGLjQQSxN4BvqRrTv9Rc07cb7uGw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"281"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Rsxu1SJZvug3H8V\u002BGLjQQSxN4BvqRrTv9Rc07cb7uGw=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/database.ixljingpm6.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\database.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ixljingpm6"},{"Name":"integrity","Value":"sha256-Orwsjt46SRnThSYkw42joMk6zBQMPRdYlYf3uBbpF5g="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/database.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"33611"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Orwsjt46SRnThSYkw42joMk6zBQMPRdYlYf3uBbpF5g=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/database.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\database.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Orwsjt46SRnThSYkw42joMk6zBQMPRdYlYf3uBbpF5g="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"33611"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Orwsjt46SRnThSYkw42joMk6zBQMPRdYlYf3uBbpF5g=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/digital.44rrl5ifdv.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\digital.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"44rrl5ifdv"},{"Name":"integrity","Value":"sha256-/PTO60biYBuld7aDKOJSh5iASXHNWVCANE\u002BF0sOzcJM="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/digital.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"273451"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022/PTO60biYBuld7aDKOJSh5iASXHNWVCANE\u002BF0sOzcJM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/digital.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\digital.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/PTO60biYBuld7aDKOJSh5iASXHNWVCANE\u002BF0sOzcJM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"273451"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022/PTO60biYBuld7aDKOJSh5iASXHNWVCANE\u002BF0sOzcJM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/gate-papers.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\gate-papers.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-2vHULelxFBWAAxxPMibb6a1jci/srW9pqLJubto\u002Bg0A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"319965"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222vHULelxFBWAAxxPMibb6a1jci/srW9pqLJubto\u002Bg0A=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/gate-papers.uehk944wbf.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\gate-papers.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"uehk944wbf"},{"Name":"integrity","Value":"sha256-2vHULelxFBWAAxxPMibb6a1jci/srW9pqLJubto\u002Bg0A="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/gate-papers.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"319965"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u00222vHULelxFBWAAxxPMibb6a1jci/srW9pqLJubto\u002Bg0A=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/html.cegjn5trd5.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\html.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"cegjn5trd5"},{"Name":"integrity","Value":"sha256-POsIzQvdBTLcxI1Tgt3jieql5JA6cPACS6PuBQ40\u002Bl0="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/html.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"3928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022POsIzQvdBTLcxI1Tgt3jieql5JA6cPACS6PuBQ40\u002Bl0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/html.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\html.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-POsIzQvdBTLcxI1Tgt3jieql5JA6cPACS6PuBQ40\u002Bl0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"3928"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022POsIzQvdBTLcxI1Tgt3jieql5JA6cPACS6PuBQ40\u002Bl0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/inorganic.dmildc4dcd.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\inorganic.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"dmildc4dcd"},{"Name":"integrity","Value":"sha256-ha6hyxPew8AipT1WzIkUGecSzJuSLtlq3\u002B8jtziMf9A="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/inorganic.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"137034"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ha6hyxPew8AipT1WzIkUGecSzJuSLtlq3\u002B8jtziMf9A=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/inorganic.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\inorganic.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ha6hyxPew8AipT1WzIkUGecSzJuSLtlq3\u002B8jtziMf9A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"137034"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022ha6hyxPew8AipT1WzIkUGecSzJuSLtlq3\u002B8jtziMf9A=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/java-course.5jxsu27ik9.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\java-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5jxsu27ik9"},{"Name":"integrity","Value":"sha256-wa6sasLQqScEPycPso0H1ndZrkgeSLPgtDVxKO0\u002Bk/w="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/java-course.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"31484"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wa6sasLQqScEPycPso0H1ndZrkgeSLPgtDVxKO0\u002Bk/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/java-course.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\java-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-wa6sasLQqScEPycPso0H1ndZrkgeSLPgtDVxKO0\u002Bk/w="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"31484"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022wa6sasLQqScEPycPso0H1ndZrkgeSLPgtDVxKO0\u002Bk/w=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/javascript-course.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\javascript-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-\u002B7dn9ZWM9i9p2HmwV4u8dssHt9T2uxaC4PY6Z3CWhco="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"32346"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022\u002B7dn9ZWM9i9p2HmwV4u8dssHt9T2uxaC4PY6Z3CWhco=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/javascript-course.ntto3yyt67.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\javascript-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ntto3yyt67"},{"Name":"integrity","Value":"sha256-\u002B7dn9ZWM9i9p2HmwV4u8dssHt9T2uxaC4PY6Z3CWhco="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/javascript-course.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"32346"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022\u002B7dn9ZWM9i9p2HmwV4u8dssHt9T2uxaC4PY6Z3CWhco=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/javascript.njevwlox79.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\javascript.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"njevwlox79"},{"Name":"integrity","Value":"sha256-Y68UeEqeOJtauz1VcP/NcVZcMWDvOy3TSIJpEYf5yBA="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/javascript.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"426275"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Y68UeEqeOJtauz1VcP/NcVZcMWDvOy3TSIJpEYf5yBA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/javascript.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\javascript.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Y68UeEqeOJtauz1VcP/NcVZcMWDvOy3TSIJpEYf5yBA="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"426275"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Y68UeEqeOJtauz1VcP/NcVZcMWDvOy3TSIJpEYf5yBA=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/math.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\math.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-EYcbDvlTqeeHwlgTwzG1O4kU\u002Bcz6mfJERM4cL\u002BK\u002BGjg="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"99043"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EYcbDvlTqeeHwlgTwzG1O4kU\u002Bcz6mfJERM4cL\u002BK\u002BGjg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/math.r9d76de1uf.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\math.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"r9d76de1uf"},{"Name":"integrity","Value":"sha256-EYcbDvlTqeeHwlgTwzG1O4kU\u002Bcz6mfJERM4cL\u002BK\u002BGjg="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/math.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"99043"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022EYcbDvlTqeeHwlgTwzG1O4kU\u002Bcz6mfJERM4cL\u002BK\u002BGjg=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/organic.g5cqt51xsi.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\organic.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"g5cqt51xsi"},{"Name":"integrity","Value":"sha256-BIH4q/jxM64QiRlNdD7qTokSDbtVE\u002BhVminJkZlwiiM="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/organic.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"40264"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022BIH4q/jxM64QiRlNdD7qTokSDbtVE\u002BhVminJkZlwiiM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/organic.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\organic.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-BIH4q/jxM64QiRlNdD7qTokSDbtVE\u002BhVminJkZlwiiM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"40264"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022BIH4q/jxM64QiRlNdD7qTokSDbtVE\u002BhVminJkZlwiiM=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/os.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\os.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-L6OImrs/POAbPyyFp/2gmDOgdbhvS5Q91VH9snYCpmQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"66185"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L6OImrs/POAbPyyFp/2gmDOgdbhvS5Q91VH9snYCpmQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/os.z7hoi5b0jv.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\os.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z7hoi5b0jv"},{"Name":"integrity","Value":"sha256-L6OImrs/POAbPyyFp/2gmDOgdbhvS5Q91VH9snYCpmQ="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/os.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"66185"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022L6OImrs/POAbPyyFp/2gmDOgdbhvS5Q91VH9snYCpmQ=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/paper-download.4dy3nw18se.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\paper-download.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4dy3nw18se"},{"Name":"integrity","Value":"sha256-uNdpIRb5Uqx5Bq2xbA\u002BEL7jI4i56pOevL/kqoLZgV1Y="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/paper-download.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"11773"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022uNdpIRb5Uqx5Bq2xbA\u002BEL7jI4i56pOevL/kqoLZgV1Y=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/paper-download.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\paper-download.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-uNdpIRb5Uqx5Bq2xbA\u002BEL7jI4i56pOevL/kqoLZgV1Y="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"11773"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022uNdpIRb5Uqx5Bq2xbA\u002BEL7jI4i56pOevL/kqoLZgV1Y=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/paper.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\paper.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-eaTUusvb\u002BIgsGnaGFR1qgVauWeBfKb6GLX3M4nZFbt0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"470"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022eaTUusvb\u002BIgsGnaGFR1qgVauWeBfKb6GLX3M4nZFbt0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/paper.x2pue7gu1o.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\paper.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x2pue7gu1o"},{"Name":"integrity","Value":"sha256-eaTUusvb\u002BIgsGnaGFR1qgVauWeBfKb6GLX3M4nZFbt0="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/paper.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"470"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022eaTUusvb\u002BIgsGnaGFR1qgVauWeBfKb6GLX3M4nZFbt0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/physical.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\physical.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Mh1IrVBOrEE4/bs/YxNNNIE8naAzNZaLXjIytOyRFy4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"134777"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Mh1IrVBOrEE4/bs/YxNNNIE8naAzNZaLXjIytOyRFy4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/physical.mbh2pcrx55.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\physical.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"mbh2pcrx55"},{"Name":"integrity","Value":"sha256-Mh1IrVBOrEE4/bs/YxNNNIE8naAzNZaLXjIytOyRFy4="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/physical.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"134777"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022Mh1IrVBOrEE4/bs/YxNNNIE8naAzNZaLXjIytOyRFy4=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/physics.5vr8zl3d47.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\physics.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5vr8zl3d47"},{"Name":"integrity","Value":"sha256-79lSVA\u002BLQKu/KzmYVVS\u002B19F1tzKZVFokCV9bT1vufns="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/physics.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"45706"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u002279lSVA\u002BLQKu/KzmYVVS\u002B19F1tzKZVFokCV9bT1vufns=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/physics.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\physics.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-79lSVA\u002BLQKu/KzmYVVS\u002B19F1tzKZVFokCV9bT1vufns="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"45706"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u002279lSVA\u002BLQKu/KzmYVVS\u002B19F1tzKZVFokCV9bT1vufns=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/projects.emkpo192qv.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\projects.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"emkpo192qv"},{"Name":"integrity","Value":"sha256-Wv8xuj343YfvdaShSmv94fvi08/u8OdJvYOxNzr4MS0="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/projects.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"198"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Wv8xuj343YfvdaShSmv94fvi08/u8OdJvYOxNzr4MS0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/projects.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\projects.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-Wv8xuj343YfvdaShSmv94fvi08/u8OdJvYOxNzr4MS0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"198"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022Wv8xuj343YfvdaShSmv94fvi08/u8OdJvYOxNzr4MS0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/python-course.9uulfyqpcw.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\python-course.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"9uulfyqpcw"},{"Name":"integrity","Value":"sha256-n1neCcWl0dvHjicAhZ5WYV4MvIKXW3tLdvmSgJvJOA0="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/python-course.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"276300"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022n1neCcWl0dvHjicAhZ5WYV4MvIKXW3tLdvmSgJvJOA0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/python-course.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\python-course.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-n1neCcWl0dvHjicAhZ5WYV4MvIKXW3tLdvmSgJvJOA0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"276300"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022n1neCcWl0dvHjicAhZ5WYV4MvIKXW3tLdvmSgJvJOA0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/web-course.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\web-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-6HUlhw3K58vaYButWCnOIvI\u002BbCC9jmuQsf0/KLxLC8U="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"69634"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00226HUlhw3K58vaYButWCnOIvI\u002BbCC9jmuQsf0/KLxLC8U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/EduVerse.API/images/courses/web-course.k9c865ejk4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\images\courses\web-course.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"k9c865ejk4"},{"Name":"integrity","Value":"sha256-6HUlhw3K58vaYButWCnOIvI\u002BbCC9jmuQsf0/KLxLC8U="},{"Name":"label","Value":"_content/EduVerse.API/images/courses/web-course.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"69634"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u00226HUlhw3K58vaYButWCnOIvI\u002BbCC9jmuQsf0/KLxLC8U=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 12:00:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>