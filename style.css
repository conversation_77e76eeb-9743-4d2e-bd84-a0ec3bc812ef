/* === Reset & Base === */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	scroll-behavior: smooth;
  }

  body {
	line-height: 1.6;
	background-color: #e6e2e2;
	color: #333;
  }

  /* === Navigation === */
  nav {
	width: 100%;
	padding: 20px 40px;
	background: #181616;
	backdrop-filter: blur(10px);
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: sticky;
	top: 0;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	z-index: 1000;
  }

  .active{
	color:#fff;
  }

  nav .logo img {
	width: 150px;
  }

  nav ul {
	display: flex;
	list-style: none;
  }

  nav ul li {
	margin: 0 20px;
  }

  nav ul li a {
	color: #222;
	text-decoration: none;
	font-weight: 600;
	transition: color 0.3s;
  }

  nav ul li a:hover {
	color: #0656eb;
  }

  .login-btn, .admin-btn {
	color: #fff;
	border: 2px solid #fff;
	border-radius: 20px;
	padding: 5px 20px;
  }

  .admin-btn {
	background-color: #DF2771;
	border-color: #DF2771;
  }

  /* === Button === */
  .get-started {
	background: #0656eb;
	color: rgb(255, 249, 249);
	padding: 10px 80px 10px 80px ; /* top, right, bottom, left */
	border-radius: 50px;
	text-decoration: none;
	font-weight: bold;
	transition: background 0.3s;
  }

  .get-started:hover {
	background: #043bb3;
  }

  /* Enroll Button */
  .enroll-btn {
    background: #DF2771;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(223, 39, 113, 0.3);
  }

  .enroll-btn:hover {
    background: #c91f5d;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(223, 39, 113, 0.4);
  }

  /* === Mobile Menu Icon === */
  .menu {
	display: none;
	width: 30px;
	cursor: pointer;
  }

  /* === Hero Section === */
  .head-container {
	height: 100vh;
	display: flex;
	align-items: center;
	justify-content: space-around;
	flex-direction: row;
	padding: 100px 50px 0;
	background: linear-gradient(rgba(0, 0, 5, 0), rgba(0, 0, 0, 0.5)),
				url("images/extra/sv2.jpg") no-repeat center center;
	background-size: cover;
	color: white;
  }

  .quote {
	padding: 50px 50px 500px 30px;
	font-size: 30px;
	margin-left: -400px;
	margin-top: 130px; /* or more if needed */
  }

  .quotehead{
	font-size: 50px;
  }



  /* === Side Menu === */
  .side-menu {
	position: fixed;
	right: -250px;
	top: 0;
	width: 250px;
	height: 100%;
	background: #222;
	color: white;
	padding: 20px;
	transition: right 0.3s;
	z-index: 1001;
  }

  .side-menu ul {
	list-style: none;
	padding-top: 30px;
  }

  .side-menu ul li {
	margin: 20px 0;
  }

  .side-menu ul li a {
	color: white;
	text-decoration: none;
	font-weight: bold;
  }

  .side-menu .close {
	text-align: right;
	cursor: pointer;
	font-size: 1.5rem;
  }

  /* === Titles & Descriptions === */
  .title p,
  .title2 span {
	font-size: 3.5rem;
	text-align: center;
	font-weight: bold;
	color: #DF2771;
	margin-bottom: 10px;
  }

  .shortdesc2 p {
	text-align: center;
	color: #666;
	font-size: 1.2rem;
  }

  /* === Course Boxes === */
  .cbox {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	padding: 40px 0;
  }

  .det {
	margin: 20px;
	background: white;
	border-radius: 15px;
	padding: 20px;
	width: 300px;
	text-align: center;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	transition: transform 0.3s ease;
  }

  .det:hover {
	transform: translateY(-5px);
  }

  .det img {
	width: 100px;
	height: 100px;
	margin-bottom: 10px;
  }

  .det a {
	text-decoration: none;
	color: #DF2771;
	font-weight: bold;
	font-size: 30px;
  }

  /* === About Section === */

  .diffSection{
	padding-bottom: 100px;
  }

  .about-content {
	display: flex;
	justify-content: space-around;
	flex-wrap: wrap;
	padding: 50px;
	gap: 30px;
	background: white;
  }

  .side-image img {
	width: 500px;
	border-radius: 15px;
	box-shadow: 0 4px 10px rgba(0,0,0,0.1);
  }

  .side-text {
	max-width: 600px;
	font-size: 18px;
  }

  .side-text h2 {
	color: #DF2771;
	margin-bottom: 20px;
  }

  /* === Extras & Stats === */
  .extra {
	text-align: center;
	font-size: 30px;
	padding: 60px 30px;
	background: #fff;
  }

  .smbox {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	gap: 50px;
  }

  .smbox .data {
	font-size: 2.9rem;
	margin-top: 20px;
	font-weight: bold;
	color: #DF2771;
  }

  .smbox .det {
	font-size: 2rem;
  }

  /* === Team Section === */
  .totalcard {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	padding: 50px;
	gap: 50px;
  }

  .card {
	width: 280px;
	background: white;
	padding: 20px;
	text-align: center;
	border-radius: 15px;
	box-shadow: 0 4px 12px rgba(0,0,0,0.1);
	transition: transform 0.3s;
  }

  .card:hover {
	transform: translateY(-5px);
  }

  .card img {
	width: 100px;
	border-radius: 50%;
  }

  .card-title {
	font-size: 1.5rem;
	color: #DF2771;
	margin: 15px 0;
  }

  .card button {
	background: #DF2771;
	color: white;
	padding: 8px 20px;
	border: none;
	border-radius: 25px;
	cursor: pointer;
	transition: background 0.3s;
  }

  .card button:hover {
	background: #bd125c;
  }

  /* === Services === */
  .service-swipe {
	padding: 50px;
	background: #f9f9f9;
	text-align: center;
  }

  .s-card {
	display: inline-block;
	width: 450px;
	background: white;
	padding:20px;
	margin: 35px;
	border-radius: 15px;
	box-shadow: 0 4px 10px rgba(0,0,0,0.1);
	transition: transform 0.3s ease;
  }

  .s-card:hover {
	transform: scale(1.05);
  }

  .s-card img {
	width: 130px;
	margin-bottom: 30px;
  }

  /* === Feedback === */
  .feedbox {
	display: flex;
	justify-content: center;
	padding: 30px;
  }

  .feed form {
	width: 350px;
	background: white;
	padding: 30px;
	border-radius: 15px;
	box-shadow: 0 0 10px rgba(0,0,0,0.1);
  }

  .feed input,
  .feed textarea {
	width: 100%;
	padding: 10px;
	margin-bottom: 15px;
	border: 1px solid #ccc;
	border-radius: 8px;
  }

  .feed button {
	background: #DF2771;
	color: white;
	padding: 10px 20px;
	border: none;
	border-radius: 25px;
	cursor: pointer;
	transition: background 0.3s;
  }

  .feed button:hover {
	background: #bd125c;
  }

  /*Marque*/

 .marqu{
 font-size: 25px;
}
  /* === Footer === */
  footer {
	background: #222;
	color: white;
	padding : 30px 1200px 100px 50px; /* top, right, bottom, left */
  }

  .footer-container {
	display: flex;
	justify-content: space-around;
  }

  .left-col img,
  .right-col img {
	margin-bottom: -5px;
  }

  .social-media a img {
	width: 30px;
	margin-right: 06px;
  }

 .info
 {
	padding: 0px 0px 0px 10px;
	font-size: 20px;
 }
  /* === Login Modal === */
.modal {
  display: none;
  position: fixed;
  z-index: 1001;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.7);
}

.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 30px;
  border-radius: 15px;
  width: 400px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.3);
  text-align: center;
}

.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #000;
}

.modal h2 {
  color: #DF2771;
  margin-bottom: 20px;
}

.login-options {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
}

.login-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #333;
  padding: 15px;
  border-radius: 10px;
  transition: all 0.3s ease;
  width: 45%;
}

.login-option:hover {
  background-color: #f5f5f5;
  transform: translateY(-5px);
}

.login-option img {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
}

.login-option.admin {
  border: 2px solid #DF2771;
}

.login-option.student {
  border: 2px solid #0656eb;
}

.login-option span {
  font-weight: bold;
}

/* === Profile Dropdown === */
.profile-dropdown {
  position: relative;
  display: inline-block;
}

.profile-btn {
  background-color: transparent;
  color: #fff;
  border: none;
  cursor: pointer;
  padding: 5px 10px;
  font-size: 16px;
}

.profile-menu {
  display: none;
  position: absolute;
  background-color: #fff;
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  z-index: 1;
  border-radius: 5px;
  right: 0;
}

.profile-menu a {
  color: #333;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  text-align: left;
}

.profile-menu a:hover {
  background-color: #f1f1f1;
  color: #DF2771;
}

/* === Responsive === */
@media screen and (max-width: 768px) {
  nav ul {
    display: none;
  }

  .menu {
    display: block;
  }

  .head-container,
  .about-content,
  .smbox,
  .totalcard,
  .footer-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .modal-content {
    width: 90%;
  }

  .login-options {
    flex-direction: column;
    align-items: center;
  }

  .login-option {
    width: 80%;
    margin-bottom: 15px;
  }
}
