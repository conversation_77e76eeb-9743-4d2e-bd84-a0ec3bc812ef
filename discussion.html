<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" type="png" href="images/icon/favicon.png">
    <title>Discussion with Tutors & Mentors - EduVerse</title>
    <link rel="stylesheet" type="text/css" href="style.css">
    <link rel="stylesheet" type="text/css" href="frontend/css/discussion.css">
    <script src="https://code.jquery.com/jquery-3.2.1.js"></script>
    <script src="frontend/js/api-service.js"></script>
    <script src="frontend/js/auth-service.js"></script>
    <script src="frontend/js/discussion.js"></script>
    <script type="text/javascript" src="script.js"></script>
    <script>
        $(window).on('scroll', function(){
            if($(window).scrollTop()){
                $('nav').addClass('black');
            } else {
                $('nav').removeClass('black');
            }
        })
    </script>
</head>
<body>
    <!-- Navigation Bar -->
    <header id="header">
        <nav>
            <div class="logo"><img src="images/icon/logo.png" alt="logo"></div>
            <ul>
                <li><a class="active" href="index.html">HOME</a></li>
                <li><a class="active" href="index.html#about_section">ABOUT</a></li>
                <li><a class="active" href="index.html#portfolio_section">PORTFOLIO</a></li>
                <li><a class="active" href="index.html#team_section">TEAM</a></li>
                <li><a class="active" href="index.html#feedBACK">FEEDBACK</a></li>
                <li><a class="active" href="computer_courses.html">COURSES</a></li>
                <li><a class="login-btn" href="login.html">LOGIN</a></li>
                <li class="user-info" style="display: none;">
                    <span class="username"></span>
                    <a href="#" class="logout-btn">LOGOUT</a>
                </li>
            </ul>
            <img src="images/icon/menu.png" class="menu" onclick="sideMenu(0)" alt="menu">
        </nav>
    </header>

    <!-- Main Content -->
    <div class="discussion-container">
        <div class="discussion-header">
            <h1>Discussion with Tutors & Mentors</h1>
            <p>Ask questions and get answers from our expert tutors and mentors</p>
        </div>

        <div class="discussion-content">
            <!-- Sidebar with tutors and mentors -->
            <div class="tutors-sidebar">
                <h2>Available Tutors</h2>
                <div class="tutor-list">
                    <div class="tutor active" data-tutor="john">
                        <img src="images/tutors/john.jpg" alt="John Smith">
                        <div class="tutor-info">
                            <h3>John Smith</h3>
                            <p>Computer Science</p>
                            <span class="status online">Online</span>
                        </div>
                    </div>
                    <div class="tutor" data-tutor="sarah">
                        <img src="images/tutors/sarah.jpg" alt="Sarah Johnson">
                        <div class="tutor-info">
                            <h3>Sarah Johnson</h3>
                            <p>Data Structures</p>
                            <span class="status online">Online</span>
                        </div>
                    </div>
                    <div class="tutor" data-tutor="michael">
                        <img src="images/tutors/Michael.jpg" alt="Michael Chen">
                        <div class="tutor-info">
                            <h3>Michael Chen</h3>
                            <p>Algorithms</p>
                            <span class="status away">Away</span>
                        </div>
                    </div>
                    <div class="tutor" data-tutor="emily">
                        <img src="images/tutors/Emily.jpg" alt="Emily Davis">
                        <div class="tutor-info">
                            <h3>Emily Davis</h3>
                            <p>Web Development</p>
                            <span class="status online">Online</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat area -->
            <div class="chat-area">
                <div class="chat-header">
                    <div class="chat-header-info">
                        <img src="images/tutors/john.jpg" alt="John Smith" id="chat-header-img">
                        <div>
                            <h3 id="chat-header-name">John Smith</h3>
                            <p id="chat-header-subject">Computer Science</p>
                        </div>
                    </div>
                    <div class="chat-header-actions">
                        <button id="end-chat-btn">End Chat</button>
                    </div>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <!-- Messages will be added here dynamically -->
                    <div class="message-welcome">
                        <p>Welcome to the discussion chat! Select a tutor from the sidebar to start a conversation or continue your existing chat.</p>
                    </div>
                </div>
                
                <div class="chat-input">
                    <textarea id="message-input" placeholder="Type your message here..."></textarea>
                    <button id="send-message-btn">Send</button>
                </div>
            </div>

            <!-- Information panel -->
            <div class="info-panel">
                <div class="tutor-profile">
                    <h2>Tutor Profile</h2>
                    <div class="profile-content" id="tutor-profile-content">
                       
                        <h3 id="profile-name">John Smith</h3>
                        <p id="profile-subject">Computer Science</p>
                        <div class="profile-details">
                            <p><strong>Experience:</strong> <span id="profile-experience">8 years</span></p>
                            <p><strong>Expertise:</strong> <span id="profile-expertise">Java, Python, Data Structures</span></p>
                            <p><strong>Rating:</strong> <span id="profile-rating">4.8/5</span></p>
                        </div>
                    </div>
                </div>
                
                <div class="faq-section">
                    <h2>Frequently Asked Questions</h2>
                    <div class="faq-item">
                        <h3>How do I start a discussion?</h3>
                        <p>Select a tutor from the sidebar and start typing your message in the chat box.</p>
                    </div>
                    <div class="faq-item">
                        <h3>Are the tutors available 24/7?</h3>
                        <p>Tutors have different availability hours. You can see their status (Online/Away) in the sidebar.</p>
                    </div>
                    <div class="faq-item">
                        <h3>Is this service free?</h3>
                        <p>Basic discussions are free for all registered users. Premium support may require a subscription.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="footer-container">
            <div class="left-col">
                <img src="images/icon/logo - Copy.png" style="width: 200px;">
                <div class="social-media">
                    <a href="#"><img src="images/icon/fb.png"></a>
                    <a href="#"><img src="images/icon/insta.png"></a>
                    <a href="#"><img src="images/icon/tt.png"></a>
                    <a href="#"><img src="images/icon/ytube.png"></a>
                    <a href="#"><img src="images/icon/linkedin.png"></a>
                </div>
                <div class="info">
                    <p><img src="images/icon/location.png">GOGTE COLLEGE OF COMMERCE BELAGAVI, KARNATAKA - 590006</p>
                    <p><img src="images/icon/phone.png"> +91-6364755850  &nbsp; +91-6363127979</p>
                    <p><img src="images/icon/mail.png">&nbsp; <EMAIL></p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
